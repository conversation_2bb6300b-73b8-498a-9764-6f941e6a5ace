graph TB
    subgraph "数据采集层"
        A[用户行为数据] --> B[点击流数据]
        C[内容特征数据] --> D[元数据提取]
        E[上下文数据] --> F[环境信息]
        G[反馈数据] --> H[评分收集]
    end

    subgraph "数据预处理层"
        I[数据清洗] --> J[异常检测]
        J --> K[数据标准化]
        K --> L[特征工程]
        L --> M[数据分割]
    end

    subgraph "用户画像层"
        N[行为分析器] --> O[时间模式分析]
        O --> P[活动偏好分析]
        P --> Q[工具使用分析]
        
        R[技能评估器] --> S[熟练度计算]
        S --> T[学习速度评估]
        T --> U[适应性分析]
        
        V[偏好推断器] --> W[风格偏好]
        W --> X[协作风格]
        X --> Y[兴趣标签]
    end

    subgraph "推荐算法层"
        Z[协同过滤] --> AA[用户相似度]
        AA --> BB[物品相似度]
        BB --> CC[矩阵分解]
        
        DD[内容推荐] --> EE[TF-IDF特征]
        EE --> FF[余弦相似度]
        FF --> GG[语义匹配]
        
        HH[深度学习] --> II[神经协同过滤]
        II --> JJ[Wide&Deep模型]
        JJ --> KK[注意力机制]
        
        LL[混合推荐] --> MM[加权融合]
        MM --> NN[投票机制]
        NN --> OO[Stacking集成]
    end

    subgraph "推荐引擎核心"
        PP[推荐调度器] --> QQ[算法选择]
        QQ --> RR[参数优化]
        RR --> SS[结果融合]
        
        TT[实时推荐] --> UU[在线学习]
        UU --> VV[增量更新]
        VV --> WW[冷启动处理]
        
        XX[多样性优化] --> YY[去重算法]
        YY --> ZZ[分散化策略]
        ZZ --> AAA[新颖性控制]
    end

    subgraph "缓存与存储层"
        BBB[推荐缓存] --> CCC[Redis集群]
        DDD[用户画像存储] --> EEE[MongoDB]
        FFF[特征存储] --> GGG[特征数据库]
        HHH[模型存储] --> III[模型仓库]
    end

    subgraph "评估与优化层"
        JJJ[离线评估] --> KKK[准确率计算]
        KKK --> LLL[召回率计算]
        LLL --> MMM[多样性评估]
        
        NNN[在线评估] --> OOO[A/B测试]
        OOO --> PPP[点击率监控]
        PPP --> QQQ[转化率分析]
        
        RRR[模型优化] --> SSS[超参数调优]
        SSS --> TTT[模型选择]
        TTT --> UUU[集成策略]
    end

    %% 数据流连接
    B --> I
    D --> I
    F --> I
    H --> I
    
    M --> N
    M --> R
    M --> V
    
    N --> Z
    R --> DD
    V --> HH
    
    Z --> LL
    DD --> LL
    HH --> LL
    
    LL --> PP
    PP --> TT
    TT --> XX
    
    XX --> BBB
    N --> DDD
    L --> FFF
    LL --> HHH
    
    PP --> JJJ
    TT --> NNN
    JJJ --> RRR
    NNN --> RRR

    %% 样式定义
    classDef dataLayer fill:#e3f2fd
    classDef processLayer fill:#f1f8e9
    classDef profileLayer fill:#fff3e0
    classDef algoLayer fill:#fce4ec
    classDef coreLayer fill:#f3e5f5
    classDef storageLayer fill:#e8f5e8
    classDef evalLayer fill:#fff8e1

    class A,B,C,D,E,F,G,H dataLayer
    class I,J,K,L,M processLayer
    class N,O,P,Q,R,S,T,U,V,W,X,Y profileLayer
    class Z,AA,BB,CC,DD,EE,FF,GG,HH,II,JJ,KK,LL,MM,NN,OO algoLayer
    class PP,QQ,RR,SS,TT,UU,VV,WW,XX,YY,ZZ,AAA coreLayer
    class BBB,CCC,DDD,EEE,FFF,GGG,HHH,III storageLayer
    class JJJ,KKK,LLL,MMM,NNN,OOO,PPP,QQQ,RRR,SSS,TTT,UUU evalLayer