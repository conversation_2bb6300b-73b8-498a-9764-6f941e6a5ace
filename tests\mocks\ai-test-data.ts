/**
 * AI测试模拟数据
 * 提供测试所需的模拟数据和工厂函数
 */

// AI模型模拟数据
export const mockAIModels = [
  {
    id: 'model-bert-001',
    name: 'test-bert',
    displayName: '测试BERT模型',
    description: '用于文本分析的BERT模型',
    type: 'bert',
    purpose: 'text_analysis',
    status: 'ready',
    currentVersion: '1.0.0',
    filePath: '/models/bert/model.bin',
    fileSize: 512,
    fileHash: 'abc123def456',
    hardwareRequirements: {
      minCPU: 'Intel i5',
      minMemory: 1024,
      minStorage: 2048
    },
    config: {
      maxTokens: 512,
      temperature: 0.7
    },
    performanceMetrics: {
      averageLatency: 150,
      throughput: 20,
      accuracy: 0.92,
      memoryUsage: 800,
      cpuUsage: 45,
      errorRate: 2.1
    },
    isActive: true,
    isDefault: true,
    priority: 8,
    tags: ['nlp', 'text', 'analysis'],
    provider: 'HuggingFace',
    license: 'Apache-2.0',
    usageCount: 1250,
    errorCount: 26,
    averageResponseTime: 145,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'model-gpt-001',
    name: 'test-gpt-3.5',
    displayName: '测试GPT-3.5模型',
    description: '用于文本生成的GPT模型',
    type: 'gpt',
    purpose: 'text_generation',
    status: 'ready',
    currentVersion: '1.2.0',
    filePath: '/models/gpt/model.bin',
    fileSize: 2048,
    fileHash: 'def456ghi789',
    hardwareRequirements: {
      minCPU: 'Intel i7',
      minMemory: 2048,
      minGPU: 'GTX 1060',
      minStorage: 4096
    },
    config: {
      maxTokens: 2048,
      temperature: 0.8,
      topP: 0.9
    },
    performanceMetrics: {
      averageLatency: 300,
      throughput: 10,
      accuracy: 0.88,
      memoryUsage: 1600,
      cpuUsage: 60,
      gpuUsage: 75,
      errorRate: 3.5
    },
    isActive: true,
    isDefault: false,
    priority: 9,
    tags: ['nlp', 'generation', 'chat'],
    provider: 'OpenAI',
    license: 'Commercial',
    usageCount: 850,
    errorCount: 30,
    averageResponseTime: 285,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: 'model-sd-001',
    name: 'test-stable-diffusion',
    displayName: '测试Stable Diffusion模型',
    description: '用于图像生成的Stable Diffusion模型',
    type: 'stable_diffusion',
    purpose: 'image_generation',
    status: 'ready',
    currentVersion: '2.1.0',
    filePath: '/models/sd/model.safetensors',
    fileSize: 4096,
    fileHash: 'ghi789jkl012',
    hardwareRequirements: {
      minCPU: 'Intel i7',
      minMemory: 4096,
      minGPU: 'RTX 3060',
      minStorage: 8192
    },
    config: {
      width: 512,
      height: 512,
      steps: 20,
      guidance: 7.5
    },
    performanceMetrics: {
      averageLatency: 5000,
      throughput: 2,
      memoryUsage: 3200,
      cpuUsage: 30,
      gpuUsage: 95,
      errorRate: 1.2
    },
    isActive: true,
    isDefault: false,
    priority: 7,
    tags: ['image', 'generation', 'diffusion'],
    provider: 'Stability AI',
    license: 'CreativeML Open RAIL-M',
    usageCount: 420,
    errorCount: 5,
    averageResponseTime: 4800,
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-18')
  }
];

// 用户画像模拟数据
export const mockUserProfile = {
  userId: 'test-user-1',
  demographics: {
    ageGroup: '25-34',
    location: 'China',
    profession: '3D Artist',
    experience: 3
  },
  behaviorPatterns: [
    {
      pattern: 'time_preference',
      frequency: 0.8,
      context: ['working_hours'],
      timePattern: [9, 10, 11, 14, 15, 16, 17]
    },
    {
      pattern: 'activity_preference',
      frequency: 0.7,
      context: ['modeling', 'texturing', 'animation'],
      timePattern: []
    },
    {
      pattern: 'tool_preference',
      frequency: 0.9,
      context: ['3d_modeler', 'material_editor', 'animation_timeline'],
      timePattern: []
    }
  ],
  skillAssessment: {
    overallLevel: 'intermediate',
    specificSkills: new Map([
      ['3d_modeling', 0.75],
      ['animation', 0.6],
      ['scripting', 0.4],
      ['materials', 0.8]
    ]),
    learningSpeed: 0.7,
    adaptability: 0.8
  },
  preferences: {
    preferredStyles: ['realistic', 'modern', 'minimalist'],
    skillLevel: 'intermediate',
    interests: ['architecture', 'product_design', 'game_dev'],
    workingHours: { start: 9, end: 18 },
    collaborationStyle: 'collaborative'
  },
  collaborationHistory: [
    {
      projectId: 'proj-001',
      collaborators: ['user-002', 'user-003'],
      role: 'lead_artist',
      duration: 30,
      satisfaction: 0.85,
      effectiveness: 0.9
    }
  ],
  learningProgress: {
    completedCourses: ['basic_modeling', 'material_basics'],
    currentGoals: ['advanced_animation', 'scripting_fundamentals'],
    weakAreas: ['scripting', 'optimization'],
    strongAreas: ['modeling', 'materials'],
    lastActivity: new Date('2024-01-20')
  }
};

// 场景上下文模拟数据
export const mockSceneContext = {
  userId: 'test-user-1',
  projectId: 'test-project-1',
  sceneId: 'test-scene-1',
  currentActivity: '3d_modeling',
  timeOfDay: 14,
  deviceType: 'desktop',
  userPreferences: mockUserProfile.preferences,
  sessionData: {
    duration: 45,
    actionsCount: 25,
    errorsCount: 3,
    completedTasks: ['create_cube', 'add_material', 'setup_lighting'],
    currentFocus: 'modeling'
  }
};

// 推荐结果模拟数据
export const mockRecommendations = [
  {
    id: 'rec-001',
    type: 'asset',
    title: '现代办公桌',
    description: '高质量的现代办公桌3D模型，适合办公室场景',
    confidence: 0.92,
    relevanceScore: 0.88,
    metadata: {
      category: 'furniture',
      tags: ['office', 'desk', 'modern'],
      difficulty: 'beginner',
      estimatedTime: 15,
      popularity: 0.85,
      lastUpdated: new Date('2024-01-15'),
      source: 'asset_library'
    },
    actions: [
      {
        type: 'import_asset',
        label: '导入资产',
        data: { assetId: 'asset-desk-001' },
        primary: true
      },
      {
        type: 'preview_asset',
        label: '预览',
        data: { assetId: 'asset-desk-001' },
        primary: false
      }
    ]
  },
  {
    id: 'rec-002',
    type: 'scene_template',
    title: '办公室场景模板',
    description: '完整的办公室场景模板，包含家具和光照设置',
    confidence: 0.87,
    relevanceScore: 0.82,
    metadata: {
      category: 'scene',
      tags: ['office', 'interior', 'complete'],
      difficulty: 'intermediate',
      estimatedTime: 30,
      popularity: 0.78,
      lastUpdated: new Date('2024-01-12'),
      source: 'template_library'
    },
    actions: [
      {
        type: 'load_template',
        label: '加载模板',
        data: { templateId: 'template-office-001' },
        primary: true
      }
    ]
  },
  {
    id: 'rec-003',
    type: 'material',
    title: '木质纹理材质',
    description: '高质量的木质纹理材质，适合家具使用',
    confidence: 0.85,
    relevanceScore: 0.79,
    metadata: {
      category: 'material',
      tags: ['wood', 'texture', 'furniture'],
      difficulty: 'beginner',
      estimatedTime: 5,
      popularity: 0.91,
      lastUpdated: new Date('2024-01-18'),
      source: 'material_library'
    },
    actions: [
      {
        type: 'apply_material',
        label: '应用材质',
        data: { materialId: 'mat-wood-001' },
        primary: true
      }
    ]
  }
];

// 推理请求模拟数据
export const mockInferenceRequests = [
  {
    modelId: 'model-bert-001',
    input: {
      text: '分析这个场景的性能问题',
      context: 'performance_analysis'
    },
    version: '1.0.0',
    parameters: {
      maxTokens: 256,
      temperature: 0.3
    }
  },
  {
    modelId: 'model-gpt-001',
    input: {
      prompt: '创建一个现代办公室场景的描述',
      context: 'scene_generation'
    },
    version: '1.2.0',
    parameters: {
      maxTokens: 512,
      temperature: 0.8
    }
  },
  {
    modelId: 'model-sd-001',
    input: {
      prompt: 'modern office interior, realistic lighting, 4k',
      negativePrompt: 'blurry, low quality',
      context: 'image_generation'
    },
    version: '2.1.0',
    parameters: {
      width: 512,
      height: 512,
      steps: 20,
      guidance: 7.5
    }
  }
];

// 性能指标模拟数据
export const mockPerformanceMetrics = {
  'model-bert-001': {
    requestCount: 150,
    totalResponseTime: 22500,
    errorCount: 3,
    lastReset: new Date('2024-01-20T00:00:00Z')
  },
  'model-gpt-001': {
    requestCount: 85,
    totalResponseTime: 25500,
    errorCount: 2,
    lastReset: new Date('2024-01-20T00:00:00Z')
  },
  'model-sd-001': {
    requestCount: 42,
    totalResponseTime: 201600,
    errorCount: 1,
    lastReset: new Date('2024-01-20T00:00:00Z')
  }
};

// 聊天消息模拟数据
export const mockChatMessages = [
  {
    id: 'msg-001',
    type: 'user',
    content: '帮我创建一个立方体',
    timestamp: Date.now() - 60000,
    status: 'sent'
  },
  {
    id: 'msg-002',
    type: 'assistant',
    content: '我将为您创建一个立方体。',
    timestamp: Date.now() - 58000,
    actions: [
      {
        type: 'create_object',
        label: '创建立方体',
        params: { type: 'cube', position: [0, 0, 0] },
        primary: true
      }
    ],
    suggestions: ['修改大小', '添加材质', '设置动画'],
    metadata: {
      model: 'test-gpt-3.5',
      confidence: 0.95,
      processingTime: 150,
      tokens: 25
    }
  },
  {
    id: 'msg-003',
    type: 'user',
    content: '分析当前场景的性能',
    timestamp: Date.now() - 30000,
    status: 'sent'
  },
  {
    id: 'msg-004',
    type: 'assistant',
    content: '场景性能分析完成。当前帧率: 60fps，内存使用: 512MB，渲染调用: 45次。整体性能良好。',
    timestamp: Date.now() - 28000,
    actions: [
      {
        type: 'show_performance_report',
        label: '查看详细报告',
        params: { sceneId: 'current' },
        primary: true
      }
    ],
    suggestions: ['优化纹理', '减少多边形', '启用LOD'],
    metadata: {
      model: 'test-bert',
      confidence: 0.88,
      processingTime: 200,
      tokens: 45
    }
  }
];

// 工厂函数：创建模拟AI模型
export function createMockAIModel(overrides: Partial<any> = {}): any {
  return {
    ...mockAIModels[0],
    ...overrides,
    id: overrides.id || `model-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  };
}

// 工厂函数：创建模拟用户画像
export function createMockUserProfile(overrides: Partial<any> = {}): any {
  return {
    ...mockUserProfile,
    ...overrides,
    userId: overrides.userId || `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  };
}

// 工厂函数：创建模拟推荐
export function createMockRecommendation(overrides: Partial<any> = {}): any {
  return {
    ...mockRecommendations[0],
    ...overrides,
    id: overrides.id || `rec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  };
}

// 工厂函数：创建模拟聊天消息
export function createMockChatMessage(overrides: Partial<any> = {}): any {
  return {
    id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    type: 'user',
    content: '测试消息',
    timestamp: Date.now(),
    ...overrides
  };
}

// 工厂函数：创建模拟推理请求
export function createMockInferenceRequest(overrides: Partial<any> = {}): any {
  return {
    ...mockInferenceRequests[0],
    ...overrides
  };
}
