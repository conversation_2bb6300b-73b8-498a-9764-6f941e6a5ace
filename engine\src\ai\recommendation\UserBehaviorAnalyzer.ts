/**
 * 用户行为分析器
 * 分析用户行为模式，构建用户画像
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { 
  UserProfile, 
  UserInteraction, 
  UserFeedback, 
  BehaviorPattern,
  SkillAssessment,
  SkillLevel,
  UserPreferences,
  CollaborationStyle
} from '../AIRecommendationEngine';

export interface UserBehaviorAnalyzerConfig {
  debug?: boolean;
  analysisWindow?: number;     // 分析窗口大小(天)
  patternThreshold?: number;   // 模式识别阈值
  updateInterval?: number;     // 更新间隔(分钟)
}

/**
 * 用户行为分析器
 */
export class UserBehaviorAnalyzer {
  private config: UserBehaviorAnalyzerConfig;
  private eventEmitter: EventEmitter = new EventEmitter();
  
  // 用户画像缓存
  private userProfiles: Map<string, UserProfile> = new Map();
  
  // 用户交互历史
  private userInteractions: Map<string, UserInteraction[]> = new Map();
  
  // 行为模式缓存
  private behaviorPatterns: Map<string, BehaviorPattern[]> = new Map();

  constructor(config: UserBehaviorAnalyzerConfig = {}) {
    this.config = {
      debug: false,
      analysisWindow: 30,
      patternThreshold: 0.7,
      updateInterval: 60,
      ...config
    };

    if (this.config.debug) {
      console.log('用户行为分析器初始化完成');
    }
  }

  /**
   * 分析用户画像
   */
  public async analyzeUser(userId: string): Promise<UserProfile> {
    // 检查缓存
    if (this.userProfiles.has(userId)) {
      const profile = this.userProfiles.get(userId)!;
      // 检查是否需要更新
      if (this.shouldUpdateProfile(profile)) {
        return await this.updateUserProfile(userId);
      }
      return profile;
    }

    // 创建新的用户画像
    return await this.createUserProfile(userId);
  }

  /**
   * 创建用户画像
   */
  private async createUserProfile(userId: string): Promise<UserProfile> {
    const interactions = await this.getUserInteractions(userId);
    const behaviorPatterns = await this.analyzeBehaviorPatterns(userId, interactions);
    const skillAssessment = await this.assessUserSkills(userId, interactions);
    const preferences = await this.inferUserPreferences(userId, interactions);

    const profile: UserProfile = {
      userId,
      demographics: await this.getUserDemographics(userId),
      behaviorPatterns,
      skillAssessment,
      preferences,
      collaborationHistory: await this.getCollaborationHistory(userId),
      learningProgress: await this.getLearningProgress(userId)
    };

    // 缓存用户画像
    this.userProfiles.set(userId, profile);

    if (this.config.debug) {
      console.log(`创建用户画像: ${userId}`);
    }

    return profile;
  }

  /**
   * 更新用户画像
   */
  private async updateUserProfile(userId: string): Promise<UserProfile> {
    const existingProfile = this.userProfiles.get(userId);
    if (!existingProfile) {
      return await this.createUserProfile(userId);
    }

    // 获取最新交互数据
    const recentInteractions = await this.getRecentInteractions(userId);
    
    // 更新行为模式
    const updatedPatterns = await this.updateBehaviorPatterns(
      userId, 
      existingProfile.behaviorPatterns, 
      recentInteractions
    );

    // 更新技能评估
    const updatedSkills = await this.updateSkillAssessment(
      existingProfile.skillAssessment,
      recentInteractions
    );

    // 更新偏好
    const updatedPreferences = await this.updateUserPreferences(
      existingProfile.preferences,
      recentInteractions
    );

    const updatedProfile: UserProfile = {
      ...existingProfile,
      behaviorPatterns: updatedPatterns,
      skillAssessment: updatedSkills,
      preferences: updatedPreferences,
      learningProgress: await this.updateLearningProgress(userId, existingProfile.learningProgress)
    };

    this.userProfiles.set(userId, updatedProfile);

    if (this.config.debug) {
      console.log(`更新用户画像: ${userId}`);
    }

    return updatedProfile;
  }

  /**
   * 分析行为模式
   */
  private async analyzeBehaviorPatterns(
    userId: string, 
    interactions: UserInteraction[]
  ): Promise<BehaviorPattern[]> {
    const patterns: BehaviorPattern[] = [];

    // 分析时间模式
    const timePattern = this.analyzeTimePattern(interactions);
    if (timePattern.confidence > this.config.patternThreshold!) {
      patterns.push({
        pattern: 'time_preference',
        frequency: timePattern.frequency,
        context: ['working_hours'],
        timePattern: timePattern.hours
      });
    }

    // 分析活动模式
    const activityPattern = this.analyzeActivityPattern(interactions);
    if (activityPattern.confidence > this.config.patternThreshold!) {
      patterns.push({
        pattern: 'activity_preference',
        frequency: activityPattern.frequency,
        context: activityPattern.activities,
        timePattern: []
      });
    }

    // 分析工具使用模式
    const toolPattern = this.analyzeToolUsagePattern(interactions);
    if (toolPattern.confidence > this.config.patternThreshold!) {
      patterns.push({
        pattern: 'tool_preference',
        frequency: toolPattern.frequency,
        context: toolPattern.tools,
        timePattern: []
      });
    }

    return patterns;
  }

  /**
   * 分析时间模式
   */
  private analyzeTimePattern(interactions: UserInteraction[]): {
    confidence: number;
    frequency: number;
    hours: number[];
  } {
    const hourCounts = new Array(24).fill(0);
    
    interactions.forEach(interaction => {
      const hour = interaction.timestamp.getHours();
      hourCounts[hour]++;
    });

    const totalInteractions = interactions.length;
    const maxCount = Math.max(...hourCounts);
    const activeHours = hourCounts.filter(count => count > 0).length;

    return {
      confidence: activeHours > 0 ? maxCount / totalInteractions : 0,
      frequency: totalInteractions / this.config.analysisWindow!,
      hours: hourCounts.map((count, hour) => count > totalInteractions * 0.1 ? hour : -1)
               .filter(hour => hour !== -1)
    };
  }

  /**
   * 分析活动模式
   */
  private analyzeActivityPattern(interactions: UserInteraction[]): {
    confidence: number;
    frequency: number;
    activities: string[];
  } {
    const activityCounts: Map<string, number> = new Map();
    
    interactions.forEach(interaction => {
      const count = activityCounts.get(interaction.action) || 0;
      activityCounts.set(interaction.action, count + 1);
    });

    const totalInteractions = interactions.length;
    const topActivities = Array.from(activityCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([activity]) => activity);

    const topActivityCount = Math.max(...Array.from(activityCounts.values()));

    return {
      confidence: totalInteractions > 0 ? topActivityCount / totalInteractions : 0,
      frequency: totalInteractions / this.config.analysisWindow!,
      activities: topActivities
    };
  }

  /**
   * 分析工具使用模式
   */
  private analyzeToolUsagePattern(interactions: UserInteraction[]): {
    confidence: number;
    frequency: number;
    tools: string[];
  } {
    const toolCounts: Map<string, number> = new Map();
    
    interactions.forEach(interaction => {
      if (interaction.context && interaction.context.tool) {
        const tool = interaction.context.tool;
        const count = toolCounts.get(tool) || 0;
        toolCounts.set(tool, count + 1);
      }
    });

    const totalToolUsage = Array.from(toolCounts.values()).reduce((sum, count) => sum + count, 0);
    const topTools = Array.from(toolCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([tool]) => tool);

    const topToolCount = Math.max(...Array.from(toolCounts.values()));

    return {
      confidence: totalToolUsage > 0 ? topToolCount / totalToolUsage : 0,
      frequency: totalToolUsage / this.config.analysisWindow!,
      tools: topTools
    };
  }

  /**
   * 评估用户技能
   */
  private async assessUserSkills(
    userId: string, 
    interactions: UserInteraction[]
  ): Promise<SkillAssessment> {
    const specificSkills = new Map<string, number>();

    // 基于交互分析技能水平
    const skillMetrics = this.calculateSkillMetrics(interactions);
    
    // 3D建模技能
    if (skillMetrics.modeling.interactions > 10) {
      specificSkills.set('3d_modeling', skillMetrics.modeling.proficiency);
    }

    // 动画技能
    if (skillMetrics.animation.interactions > 5) {
      specificSkills.set('animation', skillMetrics.animation.proficiency);
    }

    // 脚本编程技能
    if (skillMetrics.scripting.interactions > 3) {
      specificSkills.set('scripting', skillMetrics.scripting.proficiency);
    }

    // 材质设计技能
    if (skillMetrics.materials.interactions > 8) {
      specificSkills.set('materials', skillMetrics.materials.proficiency);
    }

    // 计算整体技能水平
    const overallLevel = this.calculateOverallSkillLevel(specificSkills);

    return {
      overallLevel,
      specificSkills,
      learningSpeed: skillMetrics.learningSpeed,
      adaptability: skillMetrics.adaptability
    };
  }

  /**
   * 计算技能指标
   */
  private calculateSkillMetrics(interactions: UserInteraction[]): any {
    const metrics = {
      modeling: { interactions: 0, proficiency: 0 },
      animation: { interactions: 0, proficiency: 0 },
      scripting: { interactions: 0, proficiency: 0 },
      materials: { interactions: 0, proficiency: 0 },
      learningSpeed: 0.5,
      adaptability: 0.5
    };

    // 分析各类技能的交互次数和熟练度
    interactions.forEach(interaction => {
      const action = interaction.action.toLowerCase();
      
      if (action.includes('model') || action.includes('mesh')) {
        metrics.modeling.interactions++;
        metrics.modeling.proficiency += this.calculateActionProficiency(interaction);
      } else if (action.includes('anim')) {
        metrics.animation.interactions++;
        metrics.animation.proficiency += this.calculateActionProficiency(interaction);
      } else if (action.includes('script') || action.includes('code')) {
        metrics.scripting.interactions++;
        metrics.scripting.proficiency += this.calculateActionProficiency(interaction);
      } else if (action.includes('material') || action.includes('texture')) {
        metrics.materials.interactions++;
        metrics.materials.proficiency += this.calculateActionProficiency(interaction);
      }
    });

    // 标准化熟练度分数
    Object.keys(metrics).forEach(key => {
      if (key !== 'learningSpeed' && key !== 'adaptability') {
        const skill = metrics[key as keyof typeof metrics] as any;
        if (skill.interactions > 0) {
          skill.proficiency = Math.min(1.0, skill.proficiency / skill.interactions);
        }
      }
    });

    return metrics;
  }

  /**
   * 计算操作熟练度
   */
  private calculateActionProficiency(interaction: UserInteraction): number {
    let proficiency = 0.5; // 基础分数

    // 基于操作时长评估
    if (interaction.duration) {
      if (interaction.duration < 30000) { // 30秒内完成，熟练度高
        proficiency += 0.3;
      } else if (interaction.duration > 300000) { // 5分钟以上，熟练度低
        proficiency -= 0.2;
      }
    }

    // 基于上下文信息评估
    if (interaction.context) {
      if (interaction.context.success) {
        proficiency += 0.2;
      }
      if (interaction.context.errors && interaction.context.errors > 0) {
        proficiency -= 0.1 * interaction.context.errors;
      }
    }

    return Math.max(0, Math.min(1, proficiency));
  }

  /**
   * 计算整体技能水平
   */
  private calculateOverallSkillLevel(specificSkills: Map<string, number>): SkillLevel {
    if (specificSkills.size === 0) {
      return SkillLevel.BEGINNER;
    }

    const averageSkill = Array.from(specificSkills.values())
      .reduce((sum, skill) => sum + skill, 0) / specificSkills.size;

    if (averageSkill >= 0.8) {
      return SkillLevel.EXPERT;
    } else if (averageSkill >= 0.6) {
      return SkillLevel.ADVANCED;
    } else if (averageSkill >= 0.4) {
      return SkillLevel.INTERMEDIATE;
    } else {
      return SkillLevel.BEGINNER;
    }
  }

  // 其他辅助方法的实现...
  private shouldUpdateProfile(profile: UserProfile): boolean {
    // 实现更新判断逻辑
    return false;
  }

  private async getUserInteractions(userId: string): Promise<UserInteraction[]> {
    return this.userInteractions.get(userId) || [];
  }

  private async getRecentInteractions(userId: string): Promise<UserInteraction[]> {
    const interactions = this.userInteractions.get(userId) || [];
    const cutoff = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7天前
    return interactions.filter(interaction => interaction.timestamp > cutoff);
  }

  private async getUserDemographics(userId: string): Promise<any> {
    // 实现获取用户人口统计信息
    return {};
  }

  private async getCollaborationHistory(userId: string): Promise<any[]> {
    // 实现获取协作历史
    return [];
  }

  private async getLearningProgress(userId: string): Promise<any> {
    // 实现获取学习进度
    return {};
  }

  private async inferUserPreferences(userId: string, interactions: UserInteraction[]): Promise<UserPreferences> {
    // 实现推断用户偏好
    return {
      preferredStyles: [],
      skillLevel: SkillLevel.BEGINNER,
      interests: [],
      workingHours: { start: 9, end: 17 },
      collaborationStyle: CollaborationStyle.INDEPENDENT
    };
  }

  private async updateBehaviorPatterns(userId: string, patterns: BehaviorPattern[], interactions: UserInteraction[]): Promise<BehaviorPattern[]> {
    // 实现更新行为模式
    return patterns;
  }

  private async updateSkillAssessment(assessment: SkillAssessment, interactions: UserInteraction[]): Promise<SkillAssessment> {
    // 实现更新技能评估
    return assessment;
  }

  private async updateUserPreferences(preferences: UserPreferences, interactions: UserInteraction[]): Promise<UserPreferences> {
    // 实现更新用户偏好
    return preferences;
  }

  private async updateLearningProgress(userId: string, progress: any): Promise<any> {
    // 实现更新学习进度
    return progress;
  }

  /**
   * 更新用户行为模式
   */
  public async updateBehaviorPattern(userId: string, interaction: UserInteraction): Promise<void> {
    // 添加新的交互记录
    if (!this.userInteractions.has(userId)) {
      this.userInteractions.set(userId, []);
    }
    
    const interactions = this.userInteractions.get(userId)!;
    interactions.push(interaction);

    // 保持最近1000条记录
    if (interactions.length > 1000) {
      interactions.shift();
    }

    // 触发画像更新
    if (this.userProfiles.has(userId)) {
      await this.updateUserProfile(userId);
    }
  }

  /**
   * 从反馈更新用户画像
   */
  public async updateUserProfileFromFeedback(userId: string, feedback: UserFeedback): Promise<void> {
    // 实现基于反馈的画像更新逻辑
    console.log(`基于反馈更新用户画像: ${userId}, 评分: ${feedback.rating}`);
  }
}
