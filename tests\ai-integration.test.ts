/**
 * AI功能集成测试
 * 测试底层引擎、编辑器和服务端AI功能的端到端集成
 */
import { describe, test, expect, beforeAll, afterAll, beforeEach } from '@jest/testing-library/jest-dom';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import axios from 'axios';

// 测试组件和服务
import { AIChatPanel } from '../editor/src/components/ai/AIChatPanel';
import { AIService } from '../editor/src/services/AIService';
import { EnhancedAIModelManager } from '../engine/src/ai/EnhancedAIModelManager';
import { AIRecommendationEngine } from '../engine/src/ai/AIRecommendationEngine';
import { AIContentGenerator } from '../engine/src/ai/AIContentGenerator';

// 模拟数据
import { mockAIModels, mockUserProfile, mockSceneContext } from './mocks/ai-test-data';

// 测试工具
import { createTestEngine, createTestServer, TestEnvironment } from './utils/test-environment';

describe('AI功能集成测试', () => {
  let testEnv: TestEnvironment;
  let aiService: AIService;
  let modelManager: EnhancedAIModelManager;
  let recommendationEngine: AIRecommendationEngine;
  let contentGenerator: AIContentGenerator;

  beforeAll(async () => {
    // 创建测试环境
    testEnv = await createTestEnvironment();
    
    // 初始化AI服务
    aiService = new AIService({
      apiEndpoint: testEnv.serverUrl + '/api/ai',
      model: 'test-gpt-3.5',
      maxTokens: 1000,
      temperature: 0.7,
      timeout: 10000,
      retryAttempts: 2
    });

    // 初始化底层AI组件
    modelManager = new EnhancedAIModelManager({
      debug: true,
      cache: { enabled: true, ttl: 300 }
    });

    recommendationEngine = new AIRecommendationEngine({
      debug: true,
      algorithms: [
        { name: 'collaborative', weight: 0.4, parameters: {}, enabled: true },
        { name: 'content_based', weight: 0.3, parameters: {}, enabled: true },
        { name: 'deep_learning', weight: 0.3, parameters: {}, enabled: true }
      ]
    });

    contentGenerator = new AIContentGenerator({
      debug: true,
      text3D: { timeout: 5000 },
      material: { cacheEnabled: true }
    });

    // 等待服务初始化
    await aiService.initialize();
  });

  afterAll(async () => {
    // 清理测试环境
    await testEnv.cleanup();
  });

  beforeEach(() => {
    // 重置模拟数据
    jest.clearAllMocks();
  });

  describe('AI聊天助手集成测试', () => {
    test('应该能够正常渲染AI聊天面板', () => {
      const mockOnCommand = jest.fn();
      const mockOnSuggestion = jest.fn();

      render(
        <AIChatPanel
          onCommand={mockOnCommand}
          onSuggestion={mockOnSuggestion}
          maxMessages={50}
          autoScroll={true}
          showMetadata={true}
        />
      );

      // 验证组件渲染
      expect(screen.getByText('AI助手')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('询问AI助手任何问题...')).toBeInTheDocument();
      expect(screen.getByText('创建场景')).toBeInTheDocument();
      expect(screen.getByText('性能分析')).toBeInTheDocument();
    });

    test('应该能够发送消息并接收AI响应', async () => {
      const mockOnCommand = jest.fn();
      
      // 模拟AI服务响应
      jest.spyOn(aiService, 'analyzeIntent').mockResolvedValue({
        type: 'create_object',
        confidence: 0.95,
        entities: [{ type: 'object', value: 'cube', confidence: 0.9, start: 0, end: 4 }],
        parameters: { objectType: 'cube', position: [0, 0, 0] },
        context: ['3d_modeling']
      });

      jest.spyOn(aiService, 'generateResponse').mockResolvedValue({
        text: '我将为您创建一个立方体。',
        actions: [{
          type: 'create_object',
          label: '创建立方体',
          params: { type: 'cube', position: [0, 0, 0] },
          primary: true
        }],
        suggestions: ['修改立方体大小', '添加材质', '设置动画'],
        model: 'test-gpt-3.5',
        confidence: 0.95,
        processingTime: 150,
        tokens: 25
      });

      render(
        <AIChatPanel
          onCommand={mockOnCommand}
          onSuggestion={jest.fn()}
        />
      );

      // 输入消息
      const input = screen.getByPlaceholderText('询问AI助手任何问题...');
      fireEvent.change(input, { target: { value: '创建一个立方体' } });

      // 发送消息
      const sendButton = screen.getByRole('button', { name: /send/i });
      fireEvent.click(sendButton);

      // 等待AI响应
      await waitFor(() => {
        expect(screen.getByText('我将为您创建一个立方体。')).toBeInTheDocument();
        expect(screen.getByText('创建立方体')).toBeInTheDocument();
      }, { timeout: 5000 });

      // 验证建议显示
      expect(screen.getByText('修改立方体大小')).toBeInTheDocument();
      expect(screen.getByText('添加材质')).toBeInTheDocument();
    });

    test('应该能够执行AI建议的操作', async () => {
      const mockOnCommand = jest.fn();

      // 模拟AI响应包含操作
      jest.spyOn(aiService, 'generateResponse').mockResolvedValue({
        text: '场景性能分析完成。',
        actions: [{
          type: 'analyze_performance',
          label: '查看详细报告',
          params: { sceneId: 'test-scene-1' },
          primary: true
        }],
        model: 'test-gpt-3.5',
        confidence: 0.92,
        processingTime: 200,
        tokens: 30
      });

      render(
        <AIChatPanel onCommand={mockOnCommand} />
      );

      // 触发性能分析
      const input = screen.getByPlaceholderText('询问AI助手任何问题...');
      fireEvent.change(input, { target: { value: '分析场景性能' } });
      fireEvent.click(screen.getByRole('button', { name: /send/i }));

      // 等待响应并点击操作按钮
      await waitFor(() => {
        const actionButton = screen.getByText('查看详细报告');
        fireEvent.click(actionButton);
      });

      // 验证命令被调用
      expect(mockOnCommand).toHaveBeenCalledWith('analyze_performance', {
        sceneId: 'test-scene-1'
      });
    });
  });

  describe('AI模型管理集成测试', () => {
    test('应该能够智能选择最优模型', async () => {
      // 准备测试数据
      const task = {
        type: 'text_analysis',
        priority: 3,
        requirements: {
          maxLatency: 1000,
          minAccuracy: 0.8,
          maxMemory: 1024,
          requiresGPU: false
        }
      };

      const constraints = {
        availableMemory: 2048,
        availableCPU: 4,
        availableGPU: 0,
        networkBandwidth: 100
      };

      // 执行模型选择
      const selectedModel = await modelManager.selectOptimalModel(task, constraints);

      // 验证结果
      expect(selectedModel).toBeDefined();
      expect(selectedModel.getId()).toBeTruthy();
    });

    test('应该能够预热常用模型', async () => {
      const priorities = ['critical', 'high'];
      
      // 执行模型预热
      await expect(modelManager.warmupModels(priorities)).resolves.not.toThrow();
      
      // 验证模型使用统计
      const stats = modelManager.getModelUsageStats();
      expect(stats.size).toBeGreaterThan(0);
    });

    test('应该能够动态切换模型', async () => {
      // 模拟当前模型和性能指标
      const currentModel = await modelManager.getModel('bert');
      const metrics = {
        averageLatency: 6000, // 超过阈值
        throughput: 5,
        memoryUsage: 512,
        cpuUsage: 85
      };

      // 执行动态切换
      const newModel = await modelManager.dynamicModelSwitch(currentModel, metrics);

      // 验证切换结果
      if (newModel) {
        expect(newModel.getId()).not.toBe(currentModel.getId());
      }
    });
  });

  describe('智能推荐引擎集成测试', () => {
    test('应该能够生成个性化推荐', async () => {
      const userId = 'test-user-1';
      const context = {
        userId,
        projectId: 'test-project-1',
        currentActivity: '3d_modeling',
        timeOfDay: 14,
        deviceType: 'desktop',
        userPreferences: mockUserProfile.preferences,
        sessionData: {
          duration: 30,
          actionsCount: 15,
          errorsCount: 2,
          completedTasks: ['create_cube', 'add_material'],
          currentFocus: 'modeling'
        }
      };

      // 获取资产推荐
      const recommendations = await recommendationEngine.getPersonalizedRecommendations(
        userId,
        'asset',
        context,
        5
      );

      // 验证推荐结果
      expect(recommendations).toHaveLength(5);
      expect(recommendations[0]).toHaveProperty('id');
      expect(recommendations[0]).toHaveProperty('title');
      expect(recommendations[0]).toHaveProperty('confidence');
      expect(recommendations[0].confidence).toBeGreaterThan(0);
    });

    test('应该能够更新用户反馈', async () => {
      const userId = 'test-user-1';
      const recommendationId = 'rec-123';
      const feedback = {
        rating: 4,
        comment: '很有用的推荐',
        timestamp: Date.now()
      };

      // 提交反馈
      await expect(
        recommendationEngine.updateUserFeedback(userId, recommendationId, feedback)
      ).resolves.not.toThrow();
    });

    test('应该能够实时更新推荐', async () => {
      const userId = 'test-user-1';
      const userAction = {
        userId,
        itemId: 'cube-001',
        action: 'create',
        timestamp: new Date(),
        context: { tool: '3d_modeler' },
        duration: 5000
      };

      // 更新实时推荐
      await expect(
        recommendationEngine.updateRealtimeRecommendations(userId, userAction)
      ).resolves.not.toThrow();
    });
  });

  describe('AI内容生成集成测试', () => {
    test('应该能够基于文本生成3D场景', async () => {
      const description = '创建一个现代办公室场景，包含桌子、椅子和电脑';
      const style = 'realistic';
      const constraints = {
        maxPolygons: 10000,
        maxTextureSize: 1024,
        targetFrameRate: 60
      };

      // 生成场景
      const scene = await contentGenerator.generateSceneFromText(
        description,
        style,
        constraints
      );

      // 验证生成结果
      expect(scene).toBeDefined();
      expect(scene.entities).toBeDefined();
    });

    test('应该能够生成智能材质', async () => {
      const material = await contentGenerator.generateMaterial(
        'furniture',
        'wood',
        {
          baseColor: '#8B4513',
          roughness: 0.7,
          metallic: 0.1,
          quality: 'high'
        }
      );

      // 验证材质生成
      expect(material).toBeDefined();
    });

    test('应该能够批量生成内容', async () => {
      const requests = [
        {
          id: 'req-1',
          type: 'material',
          description: '木质材质',
          parameters: { objectType: 'furniture', style: 'wood' }
        },
        {
          id: 'req-2',
          type: 'animation',
          description: '旋转动画',
          parameters: { duration: 2, type: 'rotation' }
        }
      ];

      // 批量生成
      const results = await contentGenerator.batchGenerate(requests);

      // 验证批量生成结果
      expect(results).toHaveLength(2);
      expect(results[0].status).toBe('completed');
      expect(results[1].status).toBe('completed');
    });
  });

  describe('端到端AI工作流测试', () => {
    test('完整的AI辅助场景创建流程', async () => {
      // 1. 用户通过聊天请求创建场景
      const chatRequest = '帮我创建一个科幻风格的太空站场景';
      
      // 2. AI分析意图
      const intent = await aiService.analyzeIntent(chatRequest);
      expect(intent.type).toBe('create_scene');

      // 3. 获取个性化推荐
      const recommendations = await recommendationEngine.getPersonalizedRecommendations(
        'test-user-1',
        'scene_template',
        mockSceneContext,
        3
      );
      expect(recommendations.length).toBeGreaterThan(0);

      // 4. 生成场景内容
      const scene = await contentGenerator.generateSceneFromText(
        chatRequest,
        'scifi',
        { maxPolygons: 50000 }
      );
      expect(scene).toBeDefined();

      // 5. 验证整个流程完成
      expect(intent).toBeDefined();
      expect(recommendations).toBeDefined();
      expect(scene).toBeDefined();
    });

    test('AI性能监控和优化流程', async () => {
      // 1. 模拟多次推理请求
      const requests = Array.from({ length: 10 }, (_, i) => ({
        modelId: 'test-model-1',
        input: `测试输入 ${i}`,
        version: 'latest'
      }));

      // 2. 执行推理并收集性能数据
      const results = [];
      for (const request of requests) {
        try {
          const result = await aiService.inference(request);
          results.push(result);
        } catch (error) {
          console.error('推理失败:', error);
        }
      }

      // 3. 验证性能监控
      expect(results.length).toBeGreaterThan(0);
      
      // 4. 检查是否触发了性能优化
      const modelHealth = await modelManager.getModelHealth('test-model-1');
      expect(modelHealth).toBeDefined();
      expect(modelHealth.status).toMatch(/healthy|warning|critical/);
    });
  });

  describe('错误处理和恢复测试', () => {
    test('应该能够处理AI服务连接失败', async () => {
      // 模拟网络错误
      jest.spyOn(axios, 'post').mockRejectedValue(new Error('Network Error'));

      const aiServiceWithError = new AIService({
        apiEndpoint: 'http://invalid-endpoint',
        model: 'test-model',
        timeout: 1000,
        retryAttempts: 1
      });

      // 验证错误处理
      await expect(
        aiServiceWithError.analyzeIntent('测试输入')
      ).rejects.toThrow();
    });

    test('应该能够处理模型加载失败', async () => {
      // 模拟模型加载失败
      const invalidTask = {
        type: 'invalid_type',
        priority: 1,
        requirements: {
          maxLatency: 1000,
          minAccuracy: 0.8,
          maxMemory: 1024,
          requiresGPU: false
        }
      };

      // 验证错误处理
      await expect(
        modelManager.selectOptimalModel(invalidTask, {
          availableMemory: 1024,
          availableCPU: 2,
          availableGPU: 0,
          networkBandwidth: 50
        })
      ).rejects.toThrow();
    });

    test('应该能够从错误中恢复', async () => {
      // 模拟服务重连
      await expect(aiService.reconnect()).resolves.not.toThrow();
      
      // 验证服务状态恢复
      const status = await aiService.getStatus();
      expect(status.connected).toBe(true);
    });
  });
});

/**
 * 创建测试环境
 */
async function createTestEnvironment(): Promise<TestEnvironment> {
  const server = await createTestServer();
  const engine = await createTestEngine();
  
  return {
    server,
    engine,
    serverUrl: `http://localhost:${server.port}`,
    cleanup: async () => {
      await server.close();
      await engine.destroy();
    }
  };
}
