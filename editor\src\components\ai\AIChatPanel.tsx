/**
 * AI聊天助手面板
 * 提供自然语言交互界面，支持智能对话和命令执行
 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Input, Button, Spin, Avatar, Card, Tag, Tooltip, Space, Divider } from 'antd';
import { 
  SendOutlined, 
  RobotOutlined, 
  UserOutlined, 
  CopyOutlined,
  ThumbsUpOutlined,
  ThumbsDownOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useAIAssistant } from '../../hooks/useAIAssistant';
import { useEngineService } from '../../hooks/useEngineService';
import './AIChatPanel.scss';

// 消息类型
export interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  actions?: AIAction[];
  suggestions?: string[];
  metadata?: MessageMetadata;
  status?: 'sending' | 'sent' | 'error';
}

// AI操作
export interface AIAction {
  type: string;
  label: string;
  params: any;
  primary?: boolean;
  icon?: string;
  description?: string;
}

// 消息元数据
export interface MessageMetadata {
  model?: string;
  confidence?: number;
  processingTime?: number;
  tokens?: number;
  context?: string[];
}

// 组件属性
export interface AIChatPanelProps {
  className?: string;
  onCommand?: (command: string, params: any) => void;
  onSuggestion?: (suggestion: string) => void;
  maxMessages?: number;
  autoScroll?: boolean;
  showMetadata?: boolean;
  enableVoice?: boolean;
}

/**
 * AI聊天面板组件
 */
export const AIChatPanel: React.FC<AIChatPanelProps> = ({
  className = '',
  onCommand,
  onSuggestion,
  maxMessages = 100,
  autoScroll = true,
  showMetadata = false,
  enableVoice = false
}) => {
  // 状态管理
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isConnected, setIsConnected] = useState(true);
  const [showSettings, setShowSettings] = useState(false);

  // Hooks
  const aiAssistant = useAIAssistant();
  const engineService = useEngineService();

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<any>(null);

  // 自动滚动到底部
  const scrollToBottom = useCallback(() => {
    if (autoScroll && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [autoScroll]);

  // 监听消息变化，自动滚动
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // 初始化欢迎消息
  useEffect(() => {
    const welcomeMessage: ChatMessage = {
      id: generateMessageId(),
      type: 'assistant',
      content: '你好！我是AI助手，可以帮助你进行3D场景编辑、代码生成、性能优化等工作。有什么可以帮助你的吗？',
      timestamp: Date.now(),
      suggestions: [
        '帮我创建一个基础场景',
        '分析当前场景性能',
        '推荐一些资产',
        '生成一个立方体'
      ]
    };
    setMessages([welcomeMessage]);
  }, []);

  /**
   * 处理用户输入
   */
  const handleUserInput = useCallback(async (input: string) => {
    if (!input.trim() || isTyping) return;

    const userMessage: ChatMessage = {
      id: generateMessageId(),
      type: 'user',
      content: input.trim(),
      timestamp: Date.now(),
      status: 'sending'
    };

    // 添加用户消息
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    try {
      // 更新消息状态为已发送
      setMessages(prev => prev.map(msg => 
        msg.id === userMessage.id ? { ...msg, status: 'sent' } : msg
      ));

      // 分析用户意图
      const intent = await aiAssistant.analyzeIntent(input);

      // 获取当前上下文
      const context = await engineService.getCurrentContext();

      // 生成AI响应
      const response = await aiAssistant.generateResponse(intent, {
        context,
        history: messages.slice(-5), // 最近5条消息作为上下文
        userId: 'current_user' // 实际应用中应该从用户状态获取
      });

      const assistantMessage: ChatMessage = {
        id: generateMessageId(),
        type: 'assistant',
        content: response.text,
        actions: response.actions,
        suggestions: response.suggestions,
        timestamp: Date.now(),
        metadata: {
          model: response.model,
          confidence: response.confidence,
          processingTime: response.processingTime,
          tokens: response.tokens
        }
      };

      setMessages(prev => [...prev, assistantMessage]);

      // 执行相关操作
      if (response.actions && response.actions.length > 0) {
        await executeActions(response.actions);
      }

    } catch (error) {
      console.error('AI助手响应失败:', error);
      
      const errorMessage: ChatMessage = {
        id: generateMessageId(),
        type: 'assistant',
        content: '抱歉，我遇到了一些问题。请稍后再试。',
        timestamp: Date.now(),
        metadata: {
          confidence: 0,
          processingTime: 0
        }
      };

      setMessages(prev => [...prev, errorMessage]);

      // 更新用户消息状态为错误
      setMessages(prev => prev.map(msg => 
        msg.id === userMessage.id ? { ...msg, status: 'error' } : msg
      ));

    } finally {
      setIsTyping(false);
    }
  }, [isTyping, messages, aiAssistant, engineService]);

  /**
   * 执行AI建议的操作
   */
  const executeActions = useCallback(async (actions: AIAction[]) => {
    for (const action of actions) {
      try {
        switch (action.type) {
          case 'create_object':
            await engineService.createEntity(action.params);
            break;
          case 'modify_property':
            await engineService.updateComponent(action.params);
            break;
          case 'generate_code':
            onCommand?.('generate_code', action.params);
            break;
          case 'optimize_scene':
            onCommand?.('optimize_scene', action.params);
            break;
          case 'analyze_performance':
            onCommand?.('analyze_performance', action.params);
            break;
          case 'recommend_assets':
            onCommand?.('recommend_assets', action.params);
            break;
          default:
            console.warn('未知的AI操作类型:', action.type);
        }
      } catch (error) {
        console.error(`执行AI操作失败: ${action.type}`, error);
      }
    }
  }, [engineService, onCommand]);

  /**
   * 处理建议点击
   */
  const handleSuggestionClick = useCallback((suggestion: string) => {
    setInputValue(suggestion);
    inputRef.current?.focus();
    onSuggestion?.(suggestion);
  }, [onSuggestion]);

  /**
   * 处理操作按钮点击
   */
  const handleActionClick = useCallback(async (action: AIAction) => {
    await executeActions([action]);
    
    // 添加系统消息
    const systemMessage: ChatMessage = {
      id: generateMessageId(),
      type: 'system',
      content: `已执行操作: ${action.label}`,
      timestamp: Date.now()
    };
    
    setMessages(prev => [...prev, systemMessage]);
  }, [executeActions]);

  /**
   * 处理消息反馈
   */
  const handleMessageFeedback = useCallback(async (messageId: string, feedback: 'positive' | 'negative') => {
    try {
      await aiAssistant.submitFeedback(messageId, feedback);
      
      // 更新消息显示反馈状态
      setMessages(prev => prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, metadata: { ...msg.metadata, feedback } }
          : msg
      ));
    } catch (error) {
      console.error('提交反馈失败:', error);
    }
  }, [aiAssistant]);

  /**
   * 复制消息内容
   */
  const handleCopyMessage = useCallback((content: string) => {
    navigator.clipboard.writeText(content).then(() => {
      // 可以添加复制成功的提示
    }).catch(err => {
      console.error('复制失败:', err);
    });
  }, []);

  /**
   * 重新生成响应
   */
  const handleRegenerateResponse = useCallback(async (messageId: string) => {
    const messageIndex = messages.findIndex(msg => msg.id === messageId);
    if (messageIndex === -1) return;

    const userMessage = messages[messageIndex - 1];
    if (!userMessage || userMessage.type !== 'user') return;

    // 移除原来的助手响应
    setMessages(prev => prev.filter(msg => msg.id !== messageId));

    // 重新处理用户输入
    await handleUserInput(userMessage.content);
  }, [messages, handleUserInput]);

  /**
   * 清空聊天记录
   */
  const handleClearChat = useCallback(() => {
    setMessages([]);
  }, []);

  /**
   * 处理键盘事件
   */
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleUserInput(inputValue);
    }
  }, [inputValue, handleUserInput]);

  return (
    <div className={`ai-chat-panel ${className}`}>
      {/* 聊天头部 */}
      <div className="chat-header">
        <div className="header-left">
          <Avatar icon={<RobotOutlined />} className="ai-avatar" />
          <div className="header-info">
            <span className="ai-name">AI助手</span>
            <div className="connection-status">
              <div className={`status-dot ${isConnected ? 'connected' : 'disconnected'}`} />
              <span className="status-text">
                {isConnected ? '已连接' : '连接中...'}
              </span>
            </div>
          </div>
        </div>
        
        <div className="header-actions">
          <Tooltip title="设置">
            <Button 
              type="text" 
              icon={<SettingOutlined />}
              onClick={() => setShowSettings(!showSettings)}
            />
          </Tooltip>
        </div>
      </div>

      {/* 消息列表 */}
      <div className="chat-messages">
        {messages.map(message => (
          <ChatMessageComponent
            key={message.id}
            message={message}
            showMetadata={showMetadata}
            onActionClick={handleActionClick}
            onSuggestionClick={handleSuggestionClick}
            onFeedback={handleMessageFeedback}
            onCopy={handleCopyMessage}
            onRegenerate={handleRegenerateResponse}
          />
        ))}
        
        {/* 正在输入指示器 */}
        {isTyping && (
          <div className="typing-indicator">
            <Avatar icon={<RobotOutlined />} size="small" />
            <div className="typing-content">
              <Spin size="small" />
              <span>AI助手正在思考...</span>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div className="chat-input">
        <Input.TextArea
          ref={inputRef}
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="询问AI助手任何问题..."
          autoSize={{ minRows: 1, maxRows: 4 }}
          disabled={isTyping}
        />
        <Button
          type="primary"
          icon={<SendOutlined />}
          onClick={() => handleUserInput(inputValue)}
          disabled={!inputValue.trim() || isTyping}
          className="send-button"
        />
      </div>

      {/* 快捷操作 */}
      <div className="quick-actions">
        <Space wrap>
          <Button 
            size="small" 
            onClick={() => handleSuggestionClick('帮我创建一个基础场景')}
          >
            创建场景
          </Button>
          <Button 
            size="small" 
            onClick={() => handleSuggestionClick('分析当前场景性能')}
          >
            性能分析
          </Button>
          <Button 
            size="small" 
            onClick={() => handleSuggestionClick('推荐一些资产')}
          >
            资产推荐
          </Button>
          <Button 
            size="small" 
            onClick={() => handleSuggestionClick('生成代码')}
          >
            代码生成
          </Button>
        </Space>
      </div>
    </div>
  );
};

// 辅助函数
function generateMessageId(): string {
  return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
