/**
 * 实时推荐缓存
 * 提供高性能的推荐结果缓存和管理
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Recommendation, CacheConfig } from '../AIRecommendationEngine';

// 缓存项
export interface CacheItem {
  key: string;
  value: Recommendation[];
  timestamp: number;
  ttl: number;                // 生存时间(秒)
  accessCount: number;        // 访问次数
  lastAccessed: number;       // 最后访问时间
  size: number;               // 数据大小(bytes)
}

// 缓存统计
export interface CacheStats {
  totalItems: number;
  totalSize: number;          // 总大小(bytes)
  hitCount: number;           // 命中次数
  missCount: number;          // 未命中次数
  hitRate: number;            // 命中率
  evictionCount: number;      // 驱逐次数
  averageAccessTime: number;  // 平均访问时间(ms)
}

// 缓存策略
export enum CacheEvictionPolicy {
  LRU = 'lru',               // 最近最少使用
  LFU = 'lfu',               // 最少使用频率
  FIFO = 'fifo',             // 先进先出
  TTL = 'ttl',               // 基于TTL
  SIZE = 'size'              // 基于大小
}

// 缓存配置
export interface RealtimeCacheConfig extends CacheConfig {
  evictionPolicy?: CacheEvictionPolicy;
  maxMemoryUsage?: number;    // 最大内存使用(MB)
  compressionEnabled?: boolean;
  persistenceEnabled?: boolean;
  cleanupInterval?: number;   // 清理间隔(秒)
}

/**
 * 实时推荐缓存
 */
export class RealtimeRecommendationCache {
  private config: RealtimeCacheConfig;
  private eventEmitter: EventEmitter = new EventEmitter();
  
  // 缓存存储
  private cache: Map<string, CacheItem> = new Map();
  
  // 访问顺序记录(用于LRU)
  private accessOrder: string[] = [];
  
  // 访问频率记录(用于LFU)
  private accessFrequency: Map<string, number> = new Map();
  
  // 缓存统计
  private stats: CacheStats = {
    totalItems: 0,
    totalSize: 0,
    hitCount: 0,
    missCount: 0,
    hitRate: 0,
    evictionCount: 0,
    averageAccessTime: 0
  };
  
  // 清理定时器
  private cleanupTimer?: NodeJS.Timeout;

  constructor(config: RealtimeCacheConfig = {}) {
    this.config = {
      enabled: true,
      ttl: 3600,
      maxSize: 1000,
      evictionPolicy: CacheEvictionPolicy.LRU,
      maxMemoryUsage: 100,
      compressionEnabled: false,
      persistenceEnabled: false,
      cleanupInterval: 300,
      ...config
    };

    if (this.config.enabled) {
      this.startCleanupTimer();
    }
  }

  /**
   * 获取缓存项
   */
  public async get(key: string): Promise<Recommendation[] | null> {
    const startTime = Date.now();

    try {
      const item = this.cache.get(key);
      
      if (!item) {
        this.stats.missCount++;
        this.updateHitRate();
        return null;
      }

      // 检查TTL
      if (this.isExpired(item)) {
        this.cache.delete(key);
        this.removeFromAccessOrder(key);
        this.stats.missCount++;
        this.updateHitRate();
        return null;
      }

      // 更新访问信息
      item.accessCount++;
      item.lastAccessed = Date.now();
      this.updateAccessOrder(key);
      this.updateAccessFrequency(key);

      this.stats.hitCount++;
      this.updateHitRate();

      // 触发缓存命中事件
      this.eventEmitter.emit('cache.hit', { key, accessTime: Date.now() - startTime });

      return item.value;

    } catch (error) {
      console.error('缓存获取失败:', error);
      this.eventEmitter.emit('cache.error', { operation: 'get', key, error });
      return null;
    } finally {
      this.updateAverageAccessTime(Date.now() - startTime);
    }
  }

  /**
   * 设置缓存项
   */
  public async set(key: string, value: Recommendation[]): Promise<void> {
    try {
      const size = this.calculateSize(value);
      const now = Date.now();

      const item: CacheItem = {
        key,
        value,
        timestamp: now,
        ttl: this.config.ttl!,
        accessCount: 1,
        lastAccessed: now,
        size
      };

      // 检查是否需要驱逐
      await this.ensureCapacity(size);

      // 存储缓存项
      this.cache.set(key, item);
      this.updateAccessOrder(key);
      this.updateAccessFrequency(key);

      // 更新统计
      this.stats.totalItems = this.cache.size;
      this.stats.totalSize += size;

      // 触发缓存设置事件
      this.eventEmitter.emit('cache.set', { key, size, ttl: item.ttl });

    } catch (error) {
      console.error('缓存设置失败:', error);
      this.eventEmitter.emit('cache.error', { operation: 'set', key, error });
    }
  }

  /**
   * 删除缓存项
   */
  public async delete(key: string): Promise<boolean> {
    try {
      const item = this.cache.get(key);
      if (!item) {
        return false;
      }

      this.cache.delete(key);
      this.removeFromAccessOrder(key);
      this.accessFrequency.delete(key);

      // 更新统计
      this.stats.totalItems = this.cache.size;
      this.stats.totalSize -= item.size;

      // 触发缓存删除事件
      this.eventEmitter.emit('cache.delete', { key, size: item.size });

      return true;

    } catch (error) {
      console.error('缓存删除失败:', error);
      this.eventEmitter.emit('cache.error', { operation: 'delete', key, error });
      return false;
    }
  }

  /**
   * 清除用户相关缓存
   */
  public async clearUserCache(userId: string): Promise<void> {
    const keysToDelete: string[] = [];

    for (const key of this.cache.keys()) {
      if (key.includes(userId)) {
        keysToDelete.push(key);
      }
    }

    for (const key of keysToDelete) {
      await this.delete(key);
    }

    this.eventEmitter.emit('cache.user.cleared', { userId, clearedCount: keysToDelete.length });
  }

  /**
   * 清空所有缓存
   */
  public async clear(): Promise<void> {
    const itemCount = this.cache.size;
    
    this.cache.clear();
    this.accessOrder = [];
    this.accessFrequency.clear();

    // 重置统计
    this.stats.totalItems = 0;
    this.stats.totalSize = 0;

    this.eventEmitter.emit('cache.cleared', { clearedCount: itemCount });
  }

  /**
   * 获取缓存统计
   */
  public getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 预热缓存
   */
  public async warmup(entries: Array<{ key: string; value: Recommendation[] }>): Promise<void> {
    const startTime = Date.now();
    let successCount = 0;

    for (const entry of entries) {
      try {
        await this.set(entry.key, entry.value);
        successCount++;
      } catch (error) {
        console.error(`缓存预热失败: ${entry.key}`, error);
      }
    }

    const duration = Date.now() - startTime;
    this.eventEmitter.emit('cache.warmup.completed', {
      totalEntries: entries.length,
      successCount,
      duration
    });
  }

  /**
   * 检查缓存健康状态
   */
  public getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // 检查命中率
    if (this.stats.hitRate < 0.5) {
      issues.push('缓存命中率过低');
      recommendations.push('考虑调整TTL或缓存策略');
    }

    // 检查内存使用
    const memoryUsageMB = this.stats.totalSize / (1024 * 1024);
    if (memoryUsageMB > this.config.maxMemoryUsage! * 0.9) {
      issues.push('内存使用接近限制');
      recommendations.push('考虑增加内存限制或启用压缩');
    }

    // 检查驱逐频率
    if (this.stats.evictionCount > this.stats.totalItems * 0.5) {
      issues.push('缓存驱逐频率过高');
      recommendations.push('考虑增加缓存大小或调整驱逐策略');
    }

    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (issues.length > 0) {
      status = issues.length > 2 ? 'critical' : 'warning';
    }

    return { status, issues, recommendations };
  }

  // 私有方法
  private isExpired(item: CacheItem): boolean {
    return Date.now() - item.timestamp > item.ttl * 1000;
  }

  private calculateSize(value: Recommendation[]): number {
    // 简单的大小估算
    return JSON.stringify(value).length * 2; // 假设每个字符2字节
  }

  private async ensureCapacity(newItemSize: number): Promise<void> {
    // 检查数量限制
    while (this.cache.size >= this.config.maxSize!) {
      await this.evictItem();
    }

    // 检查内存限制
    const maxMemoryBytes = this.config.maxMemoryUsage! * 1024 * 1024;
    while (this.stats.totalSize + newItemSize > maxMemoryBytes) {
      await this.evictItem();
    }
  }

  private async evictItem(): Promise<void> {
    let keyToEvict: string | null = null;

    switch (this.config.evictionPolicy) {
      case CacheEvictionPolicy.LRU:
        keyToEvict = this.accessOrder[0] || null;
        break;
      
      case CacheEvictionPolicy.LFU:
        keyToEvict = this.findLeastFrequentlyUsed();
        break;
      
      case CacheEvictionPolicy.FIFO:
        keyToEvict = this.cache.keys().next().value || null;
        break;
      
      case CacheEvictionPolicy.TTL:
        keyToEvict = this.findExpiredItem();
        break;
      
      case CacheEvictionPolicy.SIZE:
        keyToEvict = this.findLargestItem();
        break;
    }

    if (keyToEvict) {
      await this.delete(keyToEvict);
      this.stats.evictionCount++;
      this.eventEmitter.emit('cache.eviction', { key: keyToEvict, policy: this.config.evictionPolicy });
    }
  }

  private findLeastFrequentlyUsed(): string | null {
    let minFrequency = Infinity;
    let leastUsedKey: string | null = null;

    for (const [key, frequency] of this.accessFrequency) {
      if (frequency < minFrequency) {
        minFrequency = frequency;
        leastUsedKey = key;
      }
    }

    return leastUsedKey;
  }

  private findExpiredItem(): string | null {
    for (const [key, item] of this.cache) {
      if (this.isExpired(item)) {
        return key;
      }
    }
    return null;
  }

  private findLargestItem(): string | null {
    let maxSize = 0;
    let largestKey: string | null = null;

    for (const [key, item] of this.cache) {
      if (item.size > maxSize) {
        maxSize = item.size;
        largestKey = key;
      }
    }

    return largestKey;
  }

  private updateAccessOrder(key: string): void {
    this.removeFromAccessOrder(key);
    this.accessOrder.push(key);
  }

  private removeFromAccessOrder(key: string): void {
    const index = this.accessOrder.indexOf(key);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
  }

  private updateAccessFrequency(key: string): void {
    const current = this.accessFrequency.get(key) || 0;
    this.accessFrequency.set(key, current + 1);
  }

  private updateHitRate(): void {
    const total = this.stats.hitCount + this.stats.missCount;
    this.stats.hitRate = total > 0 ? this.stats.hitCount / total : 0;
  }

  private updateAverageAccessTime(accessTime: number): void {
    const total = this.stats.hitCount + this.stats.missCount;
    if (total === 1) {
      this.stats.averageAccessTime = accessTime;
    } else {
      this.stats.averageAccessTime = 
        (this.stats.averageAccessTime * (total - 1) + accessTime) / total;
    }
  }

  private startCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.performCleanup();
    }, this.config.cleanupInterval! * 1000);
  }

  private async performCleanup(): Promise<void> {
    const expiredKeys: string[] = [];

    for (const [key, item] of this.cache) {
      if (this.isExpired(item)) {
        expiredKeys.push(key);
      }
    }

    for (const key of expiredKeys) {
      await this.delete(key);
    }

    if (expiredKeys.length > 0) {
      this.eventEmitter.emit('cache.cleanup', { expiredCount: expiredKeys.length });
    }
  }

  /**
   * 销毁缓存
   */
  public destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }

    this.clear();
    this.eventEmitter.removeAllListeners();
  }

  /**
   * 事件监听
   */
  public on(event: string, listener: Function): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听
   */
  public off(event: string, listener: Function): void {
    this.eventEmitter.off(event, listener);
  }
}
