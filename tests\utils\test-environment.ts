/**
 * 测试环境工具
 * 提供创建和管理测试环境的工具函数
 */
import { Server } from 'http';
import express from 'express';
import { createConnection, Connection } from 'typeorm';
import { Redis } from 'ioredis';

// 测试环境接口
export interface TestEnvironment {
  server: TestServer;
  engine: TestEngine;
  serverUrl: string;
  cleanup: () => Promise<void>;
}

export interface TestServer {
  app: express.Application;
  server: Server;
  port: number;
  close: () => Promise<void>;
}

export interface TestEngine {
  modelManager: any;
  recommendationEngine: any;
  contentGenerator: any;
  destroy: () => Promise<void>;
}

/**
 * 创建测试服务器
 */
export async function createTestServer(): Promise<TestServer> {
  const app = express();
  const port = await getAvailablePort();

  // 中间件
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  // CORS
  app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Content-Length, X-Requested-With');
    
    if (req.method === 'OPTIONS') {
      res.sendStatus(200);
    } else {
      next();
    }
  });

  // 健康检查
  app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: Date.now() });
  });

  // AI服务模拟端点
  setupAIMockEndpoints(app);

  // 启动服务器
  const server = app.listen(port);

  return {
    app,
    server,
    port,
    close: () => new Promise((resolve) => {
      server.close(() => resolve());
    })
  };
}

/**
 * 创建测试引擎
 */
export async function createTestEngine(): Promise<TestEngine> {
  // 动态导入引擎模块（避免编译时依赖）
  const { EnhancedAIModelManager } = await import('../../engine/src/ai/EnhancedAIModelManager');
  const { AIRecommendationEngine } = await import('../../engine/src/ai/AIRecommendationEngine');
  const { AIContentGenerator } = await import('../../engine/src/ai/AIContentGenerator');

  const modelManager = new EnhancedAIModelManager({
    debug: true,
    cache: { enabled: true, ttl: 300 }
  });

  const recommendationEngine = new AIRecommendationEngine({
    debug: true,
    caching: { enabled: true, ttl: 300, maxSize: 100 }
  });

  const contentGenerator = new AIContentGenerator({
    debug: true,
    text3D: { timeout: 5000 }
  });

  return {
    modelManager,
    recommendationEngine,
    contentGenerator,
    destroy: async () => {
      // 清理资源
      if (modelManager.destroy) {
        await modelManager.destroy();
      }
      if (recommendationEngine.destroy) {
        await recommendationEngine.destroy();
      }
      if (contentGenerator.destroy) {
        await contentGenerator.destroy();
      }
    }
  };
}

/**
 * 设置AI服务模拟端点
 */
function setupAIMockEndpoints(app: express.Application) {
  // 意图分析端点
  app.post('/api/ai/intent/analyze', (req, res) => {
    const { text } = req.body;
    
    // 模拟意图分析
    let intent = 'general_question';
    let confidence = 0.7;
    const entities = [];

    if (text.includes('创建') || text.includes('生成')) {
      if (text.includes('场景')) {
        intent = 'create_scene';
        confidence = 0.95;
      } else if (text.includes('立方体') || text.includes('cube')) {
        intent = 'create_object';
        confidence = 0.92;
        entities.push({
          type: 'object',
          value: 'cube',
          confidence: 0.9,
          start: text.indexOf('立方体'),
          end: text.indexOf('立方体') + 3
        });
      }
    } else if (text.includes('分析') || text.includes('性能')) {
      intent = 'analyze_performance';
      confidence = 0.88;
    } else if (text.includes('推荐')) {
      intent = 'recommend_assets';
      confidence = 0.85;
    }

    res.json({
      intent: {
        type: intent,
        confidence
      },
      entities,
      parameters: extractParameters(text, intent),
      context: ['3d_modeling'],
      tokens: Math.floor(text.length / 4)
    });
  });

  // 响应生成端点
  app.post('/api/ai/chat/generate', (req, res) => {
    const { intent, context, history } = req.body;
    
    // 模拟响应生成
    const response = generateMockResponse(intent, context, history);
    
    res.json({
      response,
      model: 'test-gpt-3.5',
      confidence: 0.9,
      processingTime: Math.floor(Math.random() * 500) + 100,
      tokens: Math.floor(Math.random() * 100) + 20,
      metadata: {
        requestId: `req_${Date.now()}`,
        timestamp: Date.now()
      }
    });
  });

  // 反馈端点
  app.post('/api/ai/feedback', (req, res) => {
    const { messageId, feedback, comment } = req.body;
    
    // 模拟反馈处理
    console.log(`收到反馈: ${messageId} - ${feedback}`);
    
    res.json({ success: true });
  });

  // 能力查询端点
  app.get('/api/ai/capabilities', (req, res) => {
    res.json({
      capabilities: [
        'scene_creation',
        'code_generation',
        'performance_analysis',
        'asset_recommendation',
        'natural_language_query',
        'image_generation',
        'animation_creation'
      ]
    });
  });

  // 模型切换端点
  app.post('/api/ai/model/switch', (req, res) => {
    const { model } = req.body;
    
    // 模拟模型切换
    res.json({ 
      success: true, 
      currentModel: model,
      switchTime: Date.now()
    });
  });

  // 状态查询端点
  app.get('/api/ai/status', (req, res) => {
    res.json({
      connected: true,
      version: '1.0.0-test',
      uptime: Math.floor(Math.random() * 86400),
      models: ['test-gpt-3.5', 'test-bert', 'test-stable-diffusion'],
      currentModel: 'test-gpt-3.5'
    });
  });

  // 使用统计端点
  app.get('/api/ai/stats', (req, res) => {
    res.json({
      totalRequests: Math.floor(Math.random() * 1000),
      totalTokens: Math.floor(Math.random() * 50000),
      averageResponseTime: Math.floor(Math.random() * 500) + 200,
      errorRate: Math.random() * 5
    });
  });
}

/**
 * 提取参数
 */
function extractParameters(text: string, intent: string): Record<string, any> {
  const params: Record<string, any> = {};

  switch (intent) {
    case 'create_object':
      if (text.includes('立方体') || text.includes('cube')) {
        params.objectType = 'cube';
        params.position = [0, 0, 0];
        params.scale = [1, 1, 1];
      }
      break;
    
    case 'create_scene':
      if (text.includes('科幻')) {
        params.style = 'scifi';
      } else if (text.includes('现代')) {
        params.style = 'modern';
      } else {
        params.style = 'realistic';
      }
      break;
    
    case 'analyze_performance':
      params.sceneId = 'current';
      params.metrics = ['fps', 'memory', 'drawcalls'];
      break;
  }

  return params;
}

/**
 * 生成模拟响应
 */
function generateMockResponse(intent: any, context: any, history: any[]): any {
  const responses = {
    create_object: {
      text: '我将为您创建一个立方体。',
      actions: [{
        type: 'create_object',
        label: '创建立方体',
        params: { type: 'cube', position: [0, 0, 0] },
        primary: true
      }],
      suggestions: ['修改立方体大小', '添加材质', '设置动画']
    },
    
    create_scene: {
      text: '我将为您创建一个场景。请稍等片刻...',
      actions: [{
        type: 'generate_scene',
        label: '生成场景',
        params: { style: intent.parameters?.style || 'realistic' },
        primary: true
      }],
      suggestions: ['添加光照', '调整相机', '添加物体']
    },
    
    analyze_performance: {
      text: '场景性能分析完成。当前帧率良好，内存使用正常。',
      actions: [{
        type: 'show_performance_report',
        label: '查看详细报告',
        params: { sceneId: 'current' },
        primary: true
      }],
      suggestions: ['优化渲染', '减少多边形', '压缩纹理']
    },
    
    recommend_assets: {
      text: '根据您的项目，我推荐以下资产：',
      actions: [{
        type: 'show_recommendations',
        label: '查看推荐',
        params: { category: 'all' },
        primary: true
      }],
      suggestions: ['筛选类型', '按评分排序', '查看详情']
    },
    
    general_question: {
      text: '我是您的AI助手，可以帮助您进行3D场景编辑、代码生成、性能优化等工作。有什么可以帮助您的吗？',
      suggestions: ['创建场景', '性能分析', '资产推荐', '代码生成']
    }
  };

  return responses[intent.type] || responses.general_question;
}

/**
 * 获取可用端口
 */
async function getAvailablePort(): Promise<number> {
  const net = await import('net');
  
  return new Promise((resolve, reject) => {
    const server = net.createServer();
    
    server.listen(0, () => {
      const port = (server.address() as any)?.port;
      server.close(() => {
        resolve(port || 3000);
      });
    });
    
    server.on('error', reject);
  });
}

/**
 * 创建测试数据库连接
 */
export async function createTestDatabase(): Promise<Connection> {
  return createConnection({
    type: 'sqlite',
    database: ':memory:',
    entities: ['**/*.entity.ts'],
    synchronize: true,
    logging: false
  });
}

/**
 * 创建测试Redis连接
 */
export async function createTestRedis(): Promise<Redis> {
  // 使用内存模拟Redis
  const MockRedis = require('ioredis-mock');
  return new MockRedis();
}

/**
 * 等待条件满足
 */
export async function waitForCondition(
  condition: () => boolean | Promise<boolean>,
  timeout: number = 5000,
  interval: number = 100
): Promise<void> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    if (await condition()) {
      return;
    }
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  
  throw new Error(`条件在 ${timeout}ms 内未满足`);
}

/**
 * 模拟延迟
 */
export async function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}
