graph TB
    subgraph "用户交互层"
        A[用户输入] --> B[AI聊天面板]
        B --> C[消息组件]
        C --> D[操作按钮]
        D --> E[建议标签]
    end

    subgraph "前端处理层"
        F[useAIAssistant Hook] --> G[状态管理]
        G --> H[事件处理]
        H --> I[错误处理]
        I --> J[缓存管理]
    end

    subgraph "服务通信层"
        K[AIService] --> L[HTTP客户端]
        L --> M[请求拦截器]
        M --> N[响应拦截器]
        N --> O[重试机制]
    end

    subgraph "AI处理引擎"
        P[意图分析器] --> Q[BERT模型]
        Q --> R[实体提取]
        R --> S[置信度计算]
        
        T[响应生成器] --> U[GPT模型]
        U --> V[上下文管理]
        V --> W[模板引擎]
        
        X[操作执行器] --> Y[命令解析]
        Y --> Z[参数验证]
        Z --> AA[执行调度]
    end

    subgraph "后端AI服务"
        BB[AI模型服务] --> CC[模型管理器]
        CC --> DD[推理引擎]
        DD --> EE[性能监控]
        
        FF[自然语言处理] --> GG[预处理器]
        GG --> HH[特征提取]
        HH --> II[后处理器]
    end

    subgraph "数据存储层"
        JJ[对话历史] --> KK[MongoDB]
        LL[用户偏好] --> MM[Redis缓存]
        NN[模型数据] --> OO[文件存储]
        PP[性能指标] --> QQ[时序数据库]
    end

    %% 连接关系
    B --> F
    F --> K
    K --> P
    K --> T
    K --> X
    
    P --> BB
    T --> BB
    X --> BB
    
    BB --> FF
    FF --> JJ
    FF --> LL
    BB --> NN
    EE --> PP

    %% 样式
    classDef userLayer fill:#e1f5fe
    classDef frontendLayer fill:#f3e5f5
    classDef serviceLayer fill:#e8f5e8
    classDef aiLayer fill:#fff3e0
    classDef backendLayer fill:#fce4ec
    classDef dataLayer fill:#f1f8e9

    class A,B,C,D,E userLayer
    class F,G,H,I,J frontendLayer
    class K,L,M,N,O serviceLayer
    class P,Q,R,S,T,U,V,W,X,Y,Z,AA aiLayer
    class BB,CC,DD,EE,FF,GG,HH,II backendLayer
    class JJ,KK,LL,MM,NN,OO,PP,QQ dataLayer