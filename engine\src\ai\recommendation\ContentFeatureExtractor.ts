/**
 * 内容特征提取器
 * 从各种内容中提取特征用于推荐算法
 */
import { EventEmitter } from '../../utils/EventEmitter';

export interface ContentFeatureExtractorConfig {
  debug?: boolean;
  cacheEnabled?: boolean;
  extractionTimeout?: number;  // 提取超时时间(ms)
  maxCacheSize?: number;       // 最大缓存大小
}

// 内容类型
export enum ContentType {
  ASSET = 'asset',
  SCENE = 'scene',
  MATERIAL = 'material',
  ANIMATION = 'animation',
  SCRIPT = 'script',
  TEXTURE = 'texture',
  MODEL = 'model',
  AUDIO = 'audio'
}

// 内容特征
export interface ContentFeature {
  id: string;
  type: ContentType;
  features: FeatureVector;
  metadata: ContentMetadata;
  extractedAt: Date;
  version: string;
}

// 特征向量
export interface FeatureVector {
  visual?: VisualFeatures;
  semantic?: SemanticFeatures;
  technical?: TechnicalFeatures;
  behavioral?: BehavioralFeatures;
  similarity?: SimilarityFeatures;
}

// 视觉特征
export interface VisualFeatures {
  colorPalette: ColorInfo[];
  dominantColors: string[];
  brightness: number;          // 亮度 (0-1)
  contrast: number;           // 对比度 (0-1)
  saturation: number;         // 饱和度 (0-1)
  complexity: number;         // 复杂度 (0-1)
  style: string[];            // 风格标签
  composition: CompositionInfo;
}

// 颜色信息
export interface ColorInfo {
  color: string;              // 十六进制颜色
  percentage: number;         // 占比 (0-1)
  position: string;           // 位置描述
}

// 构图信息
export interface CompositionInfo {
  symmetry: number;           // 对称性 (0-1)
  balance: number;            // 平衡性 (0-1)
  focusPoints: Point2D[];     // 焦点位置
  ruleOfThirds: number;       // 三分法符合度 (0-1)
}

export interface Point2D {
  x: number;
  y: number;
}

// 语义特征
export interface SemanticFeatures {
  tags: string[];             // 标签
  categories: string[];       // 分类
  keywords: string[];         // 关键词
  description: string;        // 描述
  sentiment: number;          // 情感倾向 (-1 到 1)
  topics: TopicInfo[];        // 主题信息
  entities: EntityInfo[];     // 实体信息
}

// 主题信息
export interface TopicInfo {
  topic: string;
  confidence: number;         // 置信度 (0-1)
  weight: number;             // 权重 (0-1)
}

// 实体信息
export interface EntityInfo {
  entity: string;
  type: string;               // 实体类型
  confidence: number;         // 置信度 (0-1)
}

// 技术特征
export interface TechnicalFeatures {
  fileSize: number;           // 文件大小(bytes)
  dimensions: Dimensions;     // 尺寸信息
  format: string;             // 文件格式
  quality: number;            // 质量评分 (0-1)
  performance: PerformanceInfo;
  compatibility: string[];    // 兼容性
  requirements: RequirementInfo;
}

// 尺寸信息
export interface Dimensions {
  width?: number;
  height?: number;
  depth?: number;
  duration?: number;          // 持续时间(秒)
  frameRate?: number;         // 帧率
}

// 性能信息
export interface PerformanceInfo {
  renderTime: number;         // 渲染时间(ms)
  memoryUsage: number;        // 内存使用(MB)
  polygonCount?: number;      // 多边形数量
  textureSize?: number;       // 纹理大小(MB)
}

// 需求信息
export interface RequirementInfo {
  minCPU: string;
  minMemory: number;          // MB
  minGPU?: string;
  minStorage: number;         // MB
}

// 行为特征
export interface BehavioralFeatures {
  popularity: number;         // 受欢迎程度 (0-1)
  usageFrequency: number;     // 使用频率
  userRating: number;         // 用户评分 (0-5)
  downloadCount: number;      // 下载次数
  viewCount: number;          // 查看次数
  shareCount: number;         // 分享次数
  lastUsed: Date;             // 最后使用时间
  trendingScore: number;      // 趋势评分 (0-1)
}

// 相似性特征
export interface SimilarityFeatures {
  similarItems: string[];     // 相似项目ID
  clusters: string[];         // 聚类标识
  embeddings: number[];       // 嵌入向量
  fingerprint: string;        // 内容指纹
}

// 内容元数据
export interface ContentMetadata {
  title: string;
  author: string;
  createdAt: Date;
  updatedAt: Date;
  version: string;
  license: string;
  source: string;
  language: string;
  region: string;
  accessibility: AccessibilityInfo;
}

// 可访问性信息
export interface AccessibilityInfo {
  hasAltText: boolean;
  hasSubtitles: boolean;
  colorBlindFriendly: boolean;
  screenReaderCompatible: boolean;
}

/**
 * 内容特征提取器
 */
export class ContentFeatureExtractor {
  private config: ContentFeatureExtractorConfig;
  private eventEmitter: EventEmitter = new EventEmitter();
  
  // 特征缓存
  private featureCache: Map<string, ContentFeature> = new Map();
  
  // 提取器映射
  private extractors: Map<ContentType, FeatureExtractor> = new Map();

  constructor(config: ContentFeatureExtractorConfig = {}) {
    this.config = {
      debug: false,
      cacheEnabled: true,
      extractionTimeout: 30000,
      maxCacheSize: 1000,
      ...config
    };

    this.initializeExtractors();

    if (this.config.debug) {
      console.log('内容特征提取器初始化完成');
    }
  }

  /**
   * 初始化特征提取器
   */
  private initializeExtractors(): void {
    this.extractors.set(ContentType.ASSET, new AssetFeatureExtractor());
    this.extractors.set(ContentType.SCENE, new SceneFeatureExtractor());
    this.extractors.set(ContentType.MATERIAL, new MaterialFeatureExtractor());
    this.extractors.set(ContentType.ANIMATION, new AnimationFeatureExtractor());
    this.extractors.set(ContentType.SCRIPT, new ScriptFeatureExtractor());
    this.extractors.set(ContentType.TEXTURE, new TextureFeatureExtractor());
    this.extractors.set(ContentType.MODEL, new ModelFeatureExtractor());
    this.extractors.set(ContentType.AUDIO, new AudioFeatureExtractor());
  }

  /**
   * 提取内容特征
   */
  public async extractFeatures(
    contentId: string,
    contentType: ContentType,
    contentData: any
  ): Promise<ContentFeature> {
    const cacheKey = `${contentId}:${contentType}`;

    // 检查缓存
    if (this.config.cacheEnabled && this.featureCache.has(cacheKey)) {
      const cached = this.featureCache.get(cacheKey)!;
      if (this.isCacheValid(cached)) {
        return cached;
      }
    }

    try {
      // 获取对应的特征提取器
      const extractor = this.extractors.get(contentType);
      if (!extractor) {
        throw new Error(`不支持的内容类型: ${contentType}`);
      }

      // 设置提取超时
      const extractionPromise = extractor.extract(contentId, contentData);
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('特征提取超时')), this.config.extractionTimeout);
      });

      const features = await Promise.race([extractionPromise, timeoutPromise]);

      const contentFeature: ContentFeature = {
        id: contentId,
        type: contentType,
        features,
        metadata: await this.extractMetadata(contentId, contentData),
        extractedAt: new Date(),
        version: '1.0'
      };

      // 缓存结果
      if (this.config.cacheEnabled) {
        this.cacheFeature(cacheKey, contentFeature);
      }

      // 触发特征提取完成事件
      this.eventEmitter.emit('features.extracted', {
        contentId,
        contentType,
        featuresCount: this.countFeatures(features)
      });

      if (this.config.debug) {
        console.log(`特征提取完成: ${contentId}, 类型: ${contentType}`);
      }

      return contentFeature;

    } catch (error) {
      console.error(`特征提取失败: ${contentId}`, error);
      this.eventEmitter.emit('features.error', { contentId, contentType, error });
      throw error;
    }
  }

  /**
   * 批量提取特征
   */
  public async batchExtractFeatures(
    requests: Array<{
      contentId: string;
      contentType: ContentType;
      contentData: any;
    }>
  ): Promise<ContentFeature[]> {
    const results: ContentFeature[] = [];
    const batchSize = 5; // 并发处理数量

    for (let i = 0; i < requests.length; i += batchSize) {
      const batch = requests.slice(i, i + batchSize);
      const batchResults = await Promise.allSettled(
        batch.map(request => 
          this.extractFeatures(request.contentId, request.contentType, request.contentData)
        )
      );

      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          console.error(`批量特征提取失败: ${batch[index].contentId}`, result.reason);
        }
      });
    }

    return results;
  }

  /**
   * 计算内容相似性
   */
  public async calculateSimilarity(
    feature1: ContentFeature,
    feature2: ContentFeature
  ): Promise<number> {
    let similarity = 0;
    let weightSum = 0;

    // 视觉相似性
    if (feature1.features.visual && feature2.features.visual) {
      const visualSim = this.calculateVisualSimilarity(
        feature1.features.visual,
        feature2.features.visual
      );
      similarity += visualSim * 0.3;
      weightSum += 0.3;
    }

    // 语义相似性
    if (feature1.features.semantic && feature2.features.semantic) {
      const semanticSim = this.calculateSemanticSimilarity(
        feature1.features.semantic,
        feature2.features.semantic
      );
      similarity += semanticSim * 0.4;
      weightSum += 0.4;
    }

    // 技术相似性
    if (feature1.features.technical && feature2.features.technical) {
      const technicalSim = this.calculateTechnicalSimilarity(
        feature1.features.technical,
        feature2.features.technical
      );
      similarity += technicalSim * 0.2;
      weightSum += 0.2;
    }

    // 行为相似性
    if (feature1.features.behavioral && feature2.features.behavioral) {
      const behavioralSim = this.calculateBehavioralSimilarity(
        feature1.features.behavioral,
        feature2.features.behavioral
      );
      similarity += behavioralSim * 0.1;
      weightSum += 0.1;
    }

    return weightSum > 0 ? similarity / weightSum : 0;
  }

  /**
   * 查找相似内容
   */
  public async findSimilarContent(
    targetFeature: ContentFeature,
    candidateFeatures: ContentFeature[],
    threshold: number = 0.7,
    maxResults: number = 10
  ): Promise<Array<{ feature: ContentFeature; similarity: number }>> {
    const similarities: Array<{ feature: ContentFeature; similarity: number }> = [];

    for (const candidate of candidateFeatures) {
      if (candidate.id === targetFeature.id) {
        continue; // 跳过自己
      }

      const similarity = await this.calculateSimilarity(targetFeature, candidate);
      if (similarity >= threshold) {
        similarities.push({ feature: candidate, similarity });
      }
    }

    // 按相似度排序并返回前N个结果
    return similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, maxResults);
  }

  /**
   * 更新特征
   */
  public async updateFeatures(
    contentId: string,
    contentType: ContentType,
    contentData: any
  ): Promise<ContentFeature> {
    const cacheKey = `${contentId}:${contentType}`;
    
    // 清除缓存
    this.featureCache.delete(cacheKey);
    
    // 重新提取特征
    return await this.extractFeatures(contentId, contentType, contentData);
  }

  /**
   * 获取特征统计信息
   */
  public getFeatureStats(): {
    totalCached: number;
    cacheHitRate: number;
    extractionCount: number;
    errorCount: number;
  } {
    return {
      totalCached: this.featureCache.size,
      cacheHitRate: 0.85, // 示例值
      extractionCount: 0,  // 示例值
      errorCount: 0        // 示例值
    };
  }

  /**
   * 清理缓存
   */
  public clearCache(): void {
    this.featureCache.clear();
    if (this.config.debug) {
      console.log('特征缓存已清理');
    }
  }

  // 私有辅助方法
  private isCacheValid(feature: ContentFeature): boolean {
    const maxAge = 24 * 60 * 60 * 1000; // 24小时
    return Date.now() - feature.extractedAt.getTime() < maxAge;
  }

  private cacheFeature(key: string, feature: ContentFeature): void {
    // 检查缓存大小限制
    if (this.featureCache.size >= this.config.maxCacheSize!) {
      // 删除最旧的缓存项
      const oldestKey = this.featureCache.keys().next().value;
      this.featureCache.delete(oldestKey);
    }

    this.featureCache.set(key, feature);
  }

  private async extractMetadata(contentId: string, contentData: any): Promise<ContentMetadata> {
    // 实现元数据提取逻辑
    return {
      title: contentData.title || contentId,
      author: contentData.author || 'unknown',
      createdAt: contentData.createdAt || new Date(),
      updatedAt: contentData.updatedAt || new Date(),
      version: contentData.version || '1.0',
      license: contentData.license || 'unknown',
      source: contentData.source || 'local',
      language: contentData.language || 'en',
      region: contentData.region || 'global',
      accessibility: {
        hasAltText: false,
        hasSubtitles: false,
        colorBlindFriendly: false,
        screenReaderCompatible: false
      }
    };
  }

  private countFeatures(features: FeatureVector): number {
    let count = 0;
    if (features.visual) count++;
    if (features.semantic) count++;
    if (features.technical) count++;
    if (features.behavioral) count++;
    if (features.similarity) count++;
    return count;
  }

  private calculateVisualSimilarity(visual1: VisualFeatures, visual2: VisualFeatures): number {
    // 实现视觉相似性计算
    return 0.5; // 示例值
  }

  private calculateSemanticSimilarity(semantic1: SemanticFeatures, semantic2: SemanticFeatures): number {
    // 实现语义相似性计算
    return 0.5; // 示例值
  }

  private calculateTechnicalSimilarity(technical1: TechnicalFeatures, technical2: TechnicalFeatures): number {
    // 实现技术相似性计算
    return 0.5; // 示例值
  }

  private calculateBehavioralSimilarity(behavioral1: BehavioralFeatures, behavioral2: BehavioralFeatures): number {
    // 实现行为相似性计算
    return 0.5; // 示例值
  }

  /**
   * 事件监听
   */
  public on(event: string, listener: Function): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听
   */
  public off(event: string, listener: Function): void {
    this.eventEmitter.off(event, listener);
  }
}

// 特征提取器接口
export interface FeatureExtractor {
  extract(contentId: string, contentData: any): Promise<FeatureVector>;
}

// 各种特征提取器的基础实现
export class AssetFeatureExtractor implements FeatureExtractor {
  async extract(contentId: string, contentData: any): Promise<FeatureVector> {
    // 实现资产特征提取
    return {};
  }
}

export class SceneFeatureExtractor implements FeatureExtractor {
  async extract(contentId: string, contentData: any): Promise<FeatureVector> {
    // 实现场景特征提取
    return {};
  }
}

export class MaterialFeatureExtractor implements FeatureExtractor {
  async extract(contentId: string, contentData: any): Promise<FeatureVector> {
    // 实现材质特征提取
    return {};
  }
}

export class AnimationFeatureExtractor implements FeatureExtractor {
  async extract(contentId: string, contentData: any): Promise<FeatureVector> {
    // 实现动画特征提取
    return {};
  }
}

export class ScriptFeatureExtractor implements FeatureExtractor {
  async extract(contentId: string, contentData: any): Promise<FeatureVector> {
    // 实现脚本特征提取
    return {};
  }
}

export class TextureFeatureExtractor implements FeatureExtractor {
  async extract(contentId: string, contentData: any): Promise<FeatureVector> {
    // 实现纹理特征提取
    return {};
  }
}

export class ModelFeatureExtractor implements FeatureExtractor {
  async extract(contentId: string, contentData: any): Promise<FeatureVector> {
    // 实现模型特征提取
    return {};
  }
}

export class AudioFeatureExtractor implements FeatureExtractor {
  async extract(contentId: string, contentData: any): Promise<FeatureVector> {
    // 实现音频特征提取
    return {};
  }
}
