graph LR
    subgraph "用户界面层"
        A[用户] --> B[AI聊天界面]
        B --> C[推荐展示区]
        C --> D[操作反馈区]
    end

    subgraph "AI助手系统"
        E[意图识别] --> F[对话管理]
        F --> G[响应生成]
        G --> H[操作执行]
    end

    subgraph "推荐系统"
        I[用户画像] --> J[推荐算法]
        J --> K[结果排序]
        K --> L[多样性优化]
    end

    subgraph "数据交换层"
        M[用户行为数据] --> N[上下文信息]
        N --> O[反馈数据]
        O --> P[偏好更新]
    end

    subgraph "共享服务层"
        Q[模型管理] --> R[缓存服务]
        R --> S[监控服务]
        S --> T[配置管理]
    end

    %% 交互流程
    A --> E
    E --> I
    I --> J
    J --> F
    F --> C
    C --> M
    M --> P
    P --> I

    %% 共享服务连接
    E --> Q
    J --> Q
    F --> R
    K --> R
    G --> S
    L --> S

    %% 样式
    classDef uiLayer fill:#e1f5fe
    classDef aiLayer fill:#f3e5f5
    classDef recLayer fill:#e8f5e8
    classDef dataLayer fill:#fff3e0
    classDef serviceLayer fill:#fce4ec

    class A,B,C,D uiLayer
    class E,F,G,H aiLayer
    class I,J,K,L recLayer
    class M,N,O,P dataLayer
    class Q,R,S,T serviceLayer